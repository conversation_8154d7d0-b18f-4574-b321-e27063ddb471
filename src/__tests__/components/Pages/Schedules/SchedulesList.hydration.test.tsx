/**
 * Test for SchedulesList hydration and loading state issues
 */

import { SchedulesList } from '@/src/app/_components/Pages/Schedules/SchedulesList';
import { AuthProvider } from '@/src/app/_context/AuthContext';
import { render, screen, waitFor } from '@testing-library/react';

// Mock the auth actions
jest.mock('@/src/app/_actions/auth', () => ({
  getAuthStatus: jest.fn(),
  logoutUser: jest.fn(),
  sendPhoneVerificationWrapper: jest.fn(),
  verifyOtpCodeDirect: jest.fn(),
}));

// Mock the schedules API
jest.mock('@/src/app/_mocks/schedules', () => ({
  fetchUserSchedules: jest.fn(),
}));

// Mock js-cookie
jest.mock('js-cookie', () => ({
  get: jest.fn(),
  set: jest.fn(),
  remove: jest.fn(),
}));

import { getAuthStatus } from '@/src/app/_actions/auth';
import { fetchUserSchedules } from '@/src/app/_mocks/schedules';

const mockGetAuthStatus = getAuthStatus as jest.MockedFunction<typeof getAuthStatus>;
const mockFetchUserSchedules = fetchUserSchedules as jest.MockedFunction<typeof fetchUserSchedules>;

describe('SchedulesList Hydration Fix', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should show loading state while auth is loading', async () => {
    // Mock auth status to simulate loading
    mockGetAuthStatus.mockImplementation(
      () =>
        new Promise((resolve) =>
          setTimeout(
            () =>
              resolve({
                isAuthenticated: true,
                phoneNumber: '11987654321',
                token: null,
                userId: null,
                isLoading: false,
              }),
            100
          )
        )
    );

    render(
      <AuthProvider>
        <SchedulesList />
      </AuthProvider>
    );

    // Should show loading spinner initially
    expect(document.querySelector('.animate-spin')).toBeInTheDocument();
  });

  it('should load data after auth completes', async () => {
    // Mock successful auth status
    mockGetAuthStatus.mockResolvedValue({
      isAuthenticated: true,
      phoneNumber: '11987654321',
      token: null,
      userId: null,
      isLoading: false,
    });

    // Mock schedules data
    mockFetchUserSchedules.mockResolvedValue([
      {
        id: '1',
        serviceId: '1',
        serviceName: 'Test Service',
        serviceSlug: 'test-service',
        date: '2023-06-15',
        time: '14:00',
        address: {
          street: 'Test Street',
          number: '123',
          neighborhood: 'Test Neighborhood',
          city: 'Test City',
          state: 'TS',
          zipCode: '12345-678',
        },
        status: 'confirmed',
      },
    ]);

    render(
      <AuthProvider>
        <SchedulesList />
      </AuthProvider>
    );

    // Wait for auth to load and data to appear
    await waitFor(() => {
      expect(screen.getByText('Test Service')).toBeInTheDocument();
    });

    // Should not show login prompt
    expect(screen.queryByText('Você precisa estar logado')).not.toBeInTheDocument();
  });

  it('should show login prompt when not authenticated', async () => {
    // Mock unauthenticated status
    mockGetAuthStatus.mockResolvedValue({
      isAuthenticated: false,
      phoneNumber: null,
      token: null,
      userId: null,
      isLoading: false,
    });

    render(
      <AuthProvider>
        <SchedulesList />
      </AuthProvider>
    );

    // Wait for auth to load
    await waitFor(() => {
      expect(screen.getByText('Você precisa estar logado')).toBeInTheDocument();
    });

    // Should not show loading spinner
    expect(document.querySelector('.animate-spin')).not.toBeInTheDocument();
  });

  it('should handle auth loading state properly', async () => {
    let resolveAuth: (value: any) => void;
    const authPromise = new Promise((resolve) => {
      resolveAuth = resolve;
    });

    mockGetAuthStatus.mockReturnValue(authPromise);

    render(
      <AuthProvider>
        <SchedulesList />
      </AuthProvider>
    );

    // Should show loading initially
    expect(document.querySelector('.animate-spin')).toBeInTheDocument();

    // Resolve auth
    resolveAuth!({
      isAuthenticated: true,
      phoneNumber: '11987654321',
      token: null,
      userId: null,
      isLoading: false,
    });

    // Mock schedules
    mockFetchUserSchedules.mockResolvedValue([]);

    // Wait for loading to complete
    await waitFor(() => {
      expect(document.querySelector('.animate-spin')).not.toBeInTheDocument();
    });

    // Should show empty state
    expect(screen.getByText('Nenhum agendamento encontrado')).toBeInTheDocument();
  });
});
