/**
 * Tests for authentication Server Actions
 */

// Mock the environment variable before importing
const mockApiUrl = 'https://api.test.com/api/v1';
process.env.NEXT_PRIVATE_API_BASE_URL = mockApiUrl;

// Mock fetch globally
global.fetch = jest.fn();

// Mock cookies
jest.mock('next/headers', () => ({
  cookies: jest.fn(() => ({
    set: jest.fn(),
    delete: jest.fn(),
  })),
}));

import { sendPhoneVerification, verifyOtpCode } from '@/src/app/_actions/auth';

describe('Authentication Server Actions', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (global.fetch as jest.Mock).mockClear();
  });

  describe('sendPhoneVerification', () => {
    it('should successfully send phone verification', async () => {
      // Mock successful API response
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        status: 200,
        statusText: 'OK',
        url: `${mockApiUrl}/authenticator`,
        headers: {
          entries: () => [['content-type', 'application/json']],
        },
        json: async () => ({
          success: true,
          message: 'Verification code sent successfully',
        }),
      });

      const result = await sendPhoneVerification('11987654321');

      expect(result.success).toBe(true);
      expect(result.data).toEqual({
        success: true,
        message: 'Verification code sent successfully',
      });

      // Verify API call
      expect(global.fetch).toHaveBeenCalledWith(
        `${mockApiUrl}/authenticator`,
        expect.objectContaining({
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'service-provider': 'EUR',
          },
          body: JSON.stringify({
            phoneCode: '11',
            phoneNumber: '987654321',
          }),
        })
      );
    });

    it('should handle API response without success field (real API format)', async () => {
      // Mock API response that matches the real API format: { "message": "Created Successfully", "status": 201 }
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        status: 201,
        statusText: 'Created',
        url: `${mockApiUrl}/authenticator`,
        headers: {
          entries: () => [['content-type', 'application/json']],
        },
        json: async () => ({ message: 'Created Successfully', status: 201 }),
      });

      const result = await sendPhoneVerification('11987654321');

      expect(result.success).toBe(true);
      expect(result.data).toEqual({ message: 'Created Successfully', status: 201 });
    });

    it('should handle invalid phone number', async () => {
      const result = await sendPhoneVerification('123'); // Invalid phone

      expect(result.success).toBe(false);
      expect(result.error?.type).toBe('INVALID_PHONE');
      expect(result.error?.message).toContain('Número de telefone inválido');
    });

    it('should handle API error responses', async () => {
      // Mock API error response
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: false,
        status: 400,
        statusText: 'Bad Request',
        url: `${mockApiUrl}/authenticator`,
        headers: {
          entries: () => [['content-type', 'application/json']],
        },
        text: async () => 'Invalid phone number',
      });

      const result = await sendPhoneVerification('11987654321');

      expect(result.success).toBe(false);
      expect(result.error?.type).toBe('INVALID_PHONE');
      expect(result.error?.message).toBe('Número de telefone inválido');
    });

    it('should handle network errors', async () => {
      // Mock network error
      (global.fetch as jest.Mock).mockRejectedValueOnce(new Error('Network error'));

      const result = await sendPhoneVerification('11987654321');

      expect(result.success).toBe(false);
      expect(result.error?.type).toBe('NETWORK_ERROR');
      expect(result.error?.message).toBe(
        'Erro de conexão. Verifique sua internet e tente novamente.'
      );
    });
  });

  describe('verifyOtpCode', () => {
    it('should successfully verify OTP code', async () => {
      // Mock successful API response
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        status: 200,
        statusText: 'OK',
        url: `${mockApiUrl}/authenticator/confirm`,
        headers: {
          entries: () => [['content-type', 'application/json']],
        },
        json: async () => ({
          success: true,
          token: 'jwt-token-123',
          userId: 'user-456',
        }),
      });

      const result = await verifyOtpCode('11987654321', '123456');

      expect(result.success).toBe(true);
      expect(result.data).toEqual({
        success: true,
        token: 'jwt-token-123',
        userId: 'user-456',
      });

      // Verify API call
      expect(global.fetch).toHaveBeenCalledWith(
        `${mockApiUrl}/authenticator/confirm`,
        expect.objectContaining({
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'service-provider': 'EUR',
          },
          body: JSON.stringify({
            phoneCode: '11',
            phoneNumber: '987654321',
            token: '123456',
          }),
        })
      );
    });

    it('should handle invalid OTP', async () => {
      // Mock API error response
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: false,
        status: 400,
        text: async () => 'Invalid OTP',
      });

      const result = await verifyOtpCode('11987654321', '123456');

      expect(result.success).toBe(false);
      expect(result.error?.type).toBe('INVALID_OTP');
      expect(result.error?.message).toBe('Código de verificação inválido ou expirado');
    });

    it('should handle invalid phone number', async () => {
      const result = await verifyOtpCode('123', '123456'); // Invalid phone

      expect(result.success).toBe(false);
      expect(result.error?.type).toBe('INVALID_PHONE');
      expect(result.error?.message).toContain('Número de telefone inválido');
    });

    it('should handle server errors', async () => {
      // Mock server error response
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: false,
        status: 500,
        text: async () => 'Internal server error',
      });

      const result = await verifyOtpCode('11987654321', '123456');

      expect(result.success).toBe(false);
      expect(result.error?.type).toBe('SERVER_ERROR');
      expect(result.error?.message).toBe('Erro interno do servidor. Tente novamente.');
    });
  });
});
