/**
 * Security tests for OTP verification to ensure proper validation
 */

// Mock the environment variable before importing
const mockApiUrl = 'https://api.test.com/api/v1';
process.env.NEXT_PRIVATE_API_BASE_URL = mockApiUrl;

// Mock fetch globally
global.fetch = jest.fn();

// Mock cookies
jest.mock('next/headers', () => ({
  cookies: jest.fn(() => ({
    set: jest.fn(),
    delete: jest.fn(),
  })),
}));

import { verifyOtpCode } from '@/src/app/_actions/auth';

describe('OTP Security Validation', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (global.fetch as jest.Mock).mockClear();
  });

  it('should accept valid OTP with success message', async () => {
    // Mock successful API response
    (global.fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      status: 200,
      statusText: 'OK',
      url: `${mockApiUrl}/authenticator/confirm`,
      headers: {
        entries: () => [['content-type', 'application/json']],
      },
      json: async () => ({ message: 'Sucess', status: 200 }),
    });

    const result = await verifyOtpCode('11987654321', '123456');

    expect(result.success).toBe(true);
    expect(result.data).toEqual({ message: 'Sucess', status: 200 });
  });

  it('should reject invalid OTP with 404 response', async () => {
    // Mock 404 API response for invalid OTP
    (global.fetch as jest.Mock).mockResolvedValueOnce({
      ok: false,
      status: 404,
      statusText: 'Not Found',
      url: `${mockApiUrl}/authenticator/confirm`,
      headers: {
        entries: () => [['content-type', 'application/json']],
      },
      text: async () => JSON.stringify({ message: 'Token not found', status: 404 }),
    });

    const result = await verifyOtpCode('11987654321', '000000');

    expect(result.success).toBe(false);
    expect(result.error?.type).toBe('SERVER_ERROR');
    expect(result.error?.message).toBe('Sessão de verificação não encontrada');
  });

  it('should reject response without success indicators', async () => {
    // Mock response that returns 200 but without success indicators
    (global.fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      status: 200,
      statusText: 'OK',
      url: `${mockApiUrl}/authenticator/confirm`,
      headers: {
        entries: () => [['content-type', 'application/json']],
      },
      json: async () => ({ message: 'Invalid token', status: 400 }),
    });

    const result = await verifyOtpCode('11987654321', '000000');

    expect(result.success).toBe(false);
    expect(result.error?.type).toBe('INVALID_OTP');
    expect(result.error?.message).toBe('Invalid token');
  });

  it('should accept response with success field true', async () => {
    // Mock response with explicit success field
    (global.fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      status: 200,
      statusText: 'OK',
      url: `${mockApiUrl}/authenticator/confirm`,
      headers: {
        entries: () => [['content-type', 'application/json']],
      },
      json: async () => ({ success: true, token: 'jwt-123', userId: 'user-456' }),
    });

    const result = await verifyOtpCode('11987654321', '123456');

    expect(result.success).toBe(true);
    expect(result.data).toEqual({ success: true, token: 'jwt-123', userId: 'user-456' });
  });

  it('should reject response with success field false', async () => {
    // Mock response with explicit success field false
    (global.fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      status: 200,
      statusText: 'OK',
      url: `${mockApiUrl}/authenticator/confirm`,
      headers: {
        entries: () => [['content-type', 'application/json']],
      },
      json: async () => ({ success: false, error: 'Invalid OTP code' }),
    });

    const result = await verifyOtpCode('11987654321', '000000');

    expect(result.success).toBe(false);
    expect(result.error?.type).toBe('INVALID_OTP');
    expect(result.error?.message).toBe('Invalid OTP code');
  });
});
