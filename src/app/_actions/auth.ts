'use server';

import type {
  Auth<PERSON>rror,
  OtpConfirmationRequest,
  OtpConfirmationResponse,
  OtpConfirmationResult,
  PhoneVerificationRequest,
  PhoneVerificationResponse,
  PhoneVerificationResult,
} from '@/src/app/_interfaces';
import { splitBrazilianPhoneNumber } from '@/src/app/_utils/validation/phoneValidation';
import { cookies } from 'next/headers';

const API_BASE_URL = process.env.NEXT_PRIVATE_API_BASE_URL;

if (!API_BASE_URL) {
  throw new Error('NEXT_PRIVATE_API_BASE_URL environment variable is not set');
}

/**
 * Creates an AuthError object with proper typing
 */
function createAuthError(type: AuthError['type'], message: string, details?: string): AuthError {
  return { type, message, details };
}

/**
 * Server Action: Send phone verification code (FormData version)
 * Calls POST /api/v1/authenticator to validate phone and send OTP
 */
export async function sendPhoneVerificationAction(
  formData: FormData
): Promise<PhoneVerificationResult> {
  const phoneNumber = formData.get('phoneNumber') as string;

  return sendPhoneVerification(phoneNumber);
}

/**
 * Server Action: Send phone verification code (Wrapper for client calls)
 * This version handles the Next.js 15 parameter serialization issue
 */
export async function sendPhoneVerificationWrapper(
  phoneNumber: string | string[]
): Promise<PhoneVerificationResult> {
  'use server';

  // Handle the case where Next.js sends parameters as an array
  let cleanPhoneNumber: string;
  if (Array.isArray(phoneNumber)) {
    cleanPhoneNumber = String(phoneNumber[0]);
  } else {
    cleanPhoneNumber = String(phoneNumber);
  }

  return sendPhoneVerification(cleanPhoneNumber);
}

/**
 * Server Action: Send phone verification code (Direct call version)
 * This version ensures proper parameter handling for direct calls
 */
export async function sendPhoneVerificationDirect(
  phoneNumber: string
): Promise<PhoneVerificationResult> {
  'use server';

  // Handle the case where Next.js sends parameters as an array
  let cleanPhoneNumber: string;
  if (Array.isArray(phoneNumber)) {
    cleanPhoneNumber = String(phoneNumber[0]).trim();
  } else {
    cleanPhoneNumber = String(phoneNumber).trim();
  }

  return sendPhoneVerification(cleanPhoneNumber);
}

/**
 * Server Action: Send phone verification code
 * Calls POST /api/v1/authenticator to validate phone and send OTP
 */
export async function sendPhoneVerification(phoneNumber: string): Promise<PhoneVerificationResult> {
  try {
    // Ensure phoneNumber is a string (handle potential array serialization issue)
    const phoneNumberString = Array.isArray(phoneNumber) ? phoneNumber[0] : phoneNumber;

    // Validate and split the phone number
    const phoneNumberParts = splitBrazilianPhoneNumber(phoneNumberString);

    const requestPayload: PhoneVerificationRequest = {
      phoneCode: phoneNumberParts.phoneCode,
      phoneNumber: phoneNumberParts.phoneNumber,
    };

    const response = await fetch(`${API_BASE_URL}/authenticator`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'service-provider': 'EUR',
      },
      body: JSON.stringify(requestPayload),
    });

    if (!response.ok) {
      const errorText = await response.text();
      let errorMessage = 'Erro ao enviar código de verificação';

      if (response.status === 400) {
        errorMessage = 'Número de telefone inválido';
      } else if (response.status === 404) {
        errorMessage = 'Número de telefone não encontrado';
      } else if (response.status >= 500) {
        errorMessage = 'Erro interno do servidor. Tente novamente.';
      }

      return {
        success: false,
        error: createAuthError(
          response.status === 400 ? 'INVALID_PHONE' : 'SERVER_ERROR',
          errorMessage,
          errorText
        ),
      };
    }

    const data: PhoneVerificationResponse = await response.json();

    // Check if the API response indicates success
    // The API returns { "message": "Created Successfully", "status": 201 } for success
    // If there's a success field, use it; otherwise, assume success if we got here (response.ok was true)
    const isSuccess = data.success !== undefined ? data.success : true;

    if (!isSuccess) {
      return {
        success: false,
        error: createAuthError(
          'SERVER_ERROR',
          data.error || 'Falha ao enviar código de verificação',
          'API returned success: false'
        ),
      };
    }

    return {
      success: true,
      data,
    };
  } catch (error) {
    console.error('Error in sendPhoneVerification:', error);

    if (error instanceof Error && error.message.includes('Invalid Brazilian')) {
      return {
        success: false,
        error: createAuthError(
          'INVALID_PHONE',
          'Número de telefone inválido. Verifique o DDD e tente novamente.',
          error.message
        ),
      };
    }

    return {
      success: false,
      error: createAuthError(
        'NETWORK_ERROR',
        'Erro de conexão. Verifique sua internet e tente novamente.',
        error instanceof Error ? error.message : 'Unknown error'
      ),
    };
  }
}

/**
 * Server Action: Verify OTP code (FormData version)
 * Calls POST /api/v1/authenticator/confirm to validate OTP and complete authentication
 */
export async function verifyOtpCodeAction(formData: FormData): Promise<OtpConfirmationResult> {
  const phoneNumber = formData.get('phoneNumber') as string;
  const otpCode = formData.get('otpCode') as string;
  return verifyOtpCode(phoneNumber, otpCode);
}

/**
 * Server Action: Verify OTP code (Direct call version)
 * This version ensures proper parameter handling for direct calls
 */
export async function verifyOtpCodeDirect(
  phoneNumber: string,
  otpCode: string
): Promise<OtpConfirmationResult> {
  'use server';

  // Ensure we have clean string parameters
  const cleanPhoneNumber = String(phoneNumber).trim();
  const cleanOtpCode = String(otpCode).trim();

  return verifyOtpCode(cleanPhoneNumber, cleanOtpCode);
}

/**
 * Server Action: Verify OTP code
 * Calls POST /api/v1/authenticator/confirm to validate OTP and complete authentication
 */
export async function verifyOtpCode(
  phoneNumber: string,
  otpCode: string
): Promise<OtpConfirmationResult> {
  try {
    // Ensure parameters are strings (handle potential array serialization issue)
    const phoneNumberString = Array.isArray(phoneNumber) ? phoneNumber[0] : phoneNumber;
    const otpCodeString = Array.isArray(otpCode) ? otpCode[0] : otpCode;

    // Validate and split the phone number
    const phoneNumberParts = splitBrazilianPhoneNumber(phoneNumberString);

    const requestPayload: OtpConfirmationRequest = {
      phoneCode: phoneNumberParts.phoneCode,
      phoneNumber: phoneNumberParts.phoneNumber,
      token: otpCodeString,
    };

    const response = await fetch(`${API_BASE_URL}/authenticator/confirm`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'service-provider': 'EUR',
      },
      body: JSON.stringify(requestPayload),
    });

    if (!response.ok) {
      const errorText = await response.text();
      let errorMessage = 'Código de verificação inválido';

      if (response.status === 400) {
        errorMessage = 'Código de verificação inválido ou expirado';
      } else if (response.status === 404) {
        errorMessage = 'Sessão de verificação não encontrada';
      } else if (response.status >= 500) {
        errorMessage = 'Erro interno do servidor. Tente novamente.';
      }

      return {
        success: false,
        error: createAuthError(
          response.status === 400 ? 'INVALID_OTP' : 'SERVER_ERROR',
          errorMessage,
          errorText
        ),
      };
    }

    const data: OtpConfirmationResponse = await response.json();

    // SECURITY: Properly validate the API response
    // The API should return { "message": "Sucess", "status": 200 } for valid OTP
    // We need to validate both the HTTP status AND the response content
    let isSuccess = false;

    if (data.success !== undefined) {
      // If API includes success field, use it
      isSuccess = data.success;
    } else {
      // For APIs without success field, validate the response content
      // Success indicators: message contains "Sucess" or status is 200
      const hasSuccessMessage = data.message && data.message.toLowerCase().includes('sucess');
      const hasSuccessStatus = data.status === 200;

      // Only consider successful if we have clear success indicators
      isSuccess = hasSuccessMessage || hasSuccessStatus;
    }

    if (!isSuccess) {
      return {
        success: false,
        error: createAuthError(
          'INVALID_OTP',
          data.error || data.message || 'Código de verificação inválido',
          'API response indicates failure'
        ),
      };
    }

    // If authentication is successful, set HTTP-only cookies
    // Note: The current API may not return token/userId, so we handle this case
    if (data.token && data.userId) {
      const cookieStore = await cookies();

      // Set secure HTTP-only cookies
      cookieStore.set('token', data.token, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: 7 * 24 * 60 * 60, // 7 days
        path: '/',
      });

      cookieStore.set('userId', data.userId, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: 7 * 24 * 60 * 60, // 7 days
        path: '/',
      });

      cookieStore.set('phoneNumber', phoneNumberParts.fullNumber, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: 7 * 24 * 60 * 60, // 7 days
        path: '/',
      });

      return {
        success: true,
        data,
      };
    } else {
      // API returned success but no token/userId - this might be expected for some APIs
      // that only confirm the OTP without providing authentication tokens
      return {
        success: true,
        data,
      };
    }
  } catch (error) {
    console.error('Error in verifyOtpCode:', error);

    if (error instanceof Error && error.message.includes('Invalid Brazilian')) {
      return {
        success: false,
        error: createAuthError(
          'INVALID_PHONE',
          'Número de telefone inválido. Verifique o DDD e tente novamente.',
          error.message
        ),
      };
    }

    return {
      success: false,
      error: createAuthError(
        'NETWORK_ERROR',
        'Erro de conexão. Verifique sua internet e tente novamente.',
        error instanceof Error ? error.message : 'Unknown error'
      ),
    };
  }
}

/**
 * Server Action: Logout user
 * Clears authentication cookies
 */
export async function logoutUser(): Promise<void> {
  const cookieStore = await cookies();

  // Clear all authentication cookies
  cookieStore.delete('token');
  cookieStore.delete('userId');
  cookieStore.delete('phoneNumber');
}
